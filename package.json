{"name": "php-gaming-website", "version": "1.0.0", "description": "Gaming website with Tailwind CSS", "scripts": {"build-css": "cross-env NODE_ENV=development postcss ./src/input.css -o ./assets/css/tailwind.css --watch", "build-css-prod": "cross-env NODE_ENV=production postcss ./src/input.css -o ./assets/css/tailwind.css", "dev": "cross-env NODE_ENV=development postcss ./src/input.css -o ./assets/css/tailwind.css --watch", "build": "cross-env NODE_ENV=production postcss ./src/input.css -o ./assets/css/tailwind.css", "analyze-css": "cross-env NODE_ENV=production postcss ./src/input.css -o ./assets/css/tailwind-analysis.css --verbose", "clean-css": "cross-env NODE_ENV=production postcss ./src/input.css -o ./assets/css/tailwind-clean.css"}, "devDependencies": {"autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "cssnano": "^7.0.7", "postcss": "^8.5.5", "postcss-cli": "^11.0.1", "postcss-combine-duplicated-selectors": "^10.0.3", "postcss-discard-duplicates": "^7.0.2", "postcss-discard-overridden": "^7.0.1", "postcss-import": "^16.1.0", "postcss-merge-rules": "^7.0.5", "postcss-sort-media-queries": "^5.2.0", "tailwindcss": "^3.4.0"}}