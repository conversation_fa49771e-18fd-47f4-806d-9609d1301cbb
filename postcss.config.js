module.exports = (ctx) => ({
  plugins: {
    // Import processing - handles @import statements
    'postcss-import': {},
    
    // Tailwind CSS processing
    'tailwindcss': {},
    
    // Autoprefixer for vendor prefixes
    'autoprefixer': {},
    
    // CSS optimization plugins (only in production)
    ...(ctx.env === 'production' ? {
      // Remove duplicate CSS rules
      'postcss-discard-duplicates': {},
      
      // Remove overridden CSS rules (later rules override earlier ones)
      'postcss-discard-overridden': {},
      
      // Combine duplicate selectors
      'postcss-combine-duplicated-selectors': {
        removeDuplicatedProperties: true,
        removeDuplicatedValues: true
      },
      
      // Merge CSS rules with identical selectors
      'postcss-merge-rules': {},
      
      // Sort media queries for better compression
      'postcss-sort-media-queries': {
        sort: 'mobile-first'
      },
      
      // Final minification with cssnano
      'cssnano': {
        preset: ['default', {
          // Preserve important comments
          discardComments: {
            removeAll: false,
            removeAllButFirst: false
          },
          // Merge duplicate rules
          mergeLonghand: true,
          mergeRules: true,
          // Remove duplicate declarations
          discardDuplicates: true,
          // Remove overridden declarations
          discardOverridden: true,
          // Normalize and optimize values
          normalizeWhitespace: true,
          colormin: true,
          convertValues: true,
          // Optimize selectors
          minifySelectors: true,
          // Sort CSS properties for better gzip compression
          cssDeclarationSorter: {
            order: 'alphabetical'
          }
        }]
      }
    } : {})
  }
});
